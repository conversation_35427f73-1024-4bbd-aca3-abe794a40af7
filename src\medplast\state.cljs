(ns medplast.state
  (:require-macros [medplast.state :as macro])
  (:require
   [clojure.core]
   [medplast.lang :as lang]
   [reagent.core :as r]
   [medplast.firebase :as firebase]
   [medplast.firestore :as firestore]
   [cljs.core.async :as async :refer [<! go]]
   [reitit.frontend.easy :as rfe]
   ))

(defonce route-match (r/atom nil))

(r/track! #(println "route-match" (:template @route-match) (:parameters @route-match)))

(defn get-doc-id-from-match
  "Extracts the document ID from the route match parameters.
  This is typically used to get the ID of a specific document being viewed."
  [match]
  (let [{:keys [path _query]} (:parameters match)
        {:keys [id]} path]
    id))

(defn get-user-uid-from-match
  "Extracts the user UID from the route match parameters.
  This is used to identify the target user when the route includes a user ID."
  [match]
  (let [{:keys [path _query]} (:parameters match)
        {:keys [user]} path]
    user))

(defn replace-query-params!
  "Replaces the current query parameters in the browser's URL.
  Takes a map `hm` where keys are parameter names and values are their desired values."
  [hm]
  (rfe/set-query hm))

(def route-match-doc-id-atom (r/reaction (get-doc-id-from-match @route-match)))

(def route-match-user-uid-atom (r/reaction (get-user-uid-from-match @route-match)))

; (defrecord DbEntity [collection-ref]
;   ; Object
;   ; (toString [_dbe] (str "DbEntity{" (:path-segments collection-ref) "}"))

;   ; IPrintWithWriter

;   ; (-pr-writer [_new-obj writer _]
;   ;   (write-all writer "#DbEntity \"" (.-path collection-ref) "\""))

;   DbEntityProtocol

;   ; (collection-subscription-trackee [dbe]
;   ;   (trackee get-collection-subscription-atom dbe))

;   ; (doc-subscription-trackee [dbe id]
;   ;   (trackee get-doc-subscription-atom dbe id))

;   ; (<?soft-delete! [dbe id]
;   ;   (let [doc-ref (get-doc-ref dbe id)]
;   ;     (firebase/<?partial-update-doc! doc-ref {:deleted true})))

;   ; (<?soft-undelete! [dbe id]
;   ;   (let [doc-ref (get-doc-ref dbe id)]
;   ;     (firebase/<?partial-update-doc! doc-ref {:deleted firebase/field-deletion-mark})))

;   ; (<?hard-delete! [dbe id]
;   ;   (let [doc-ref (get-doc-ref dbe id)]
;   ;     (firebase/<?delete-doc! doc-ref)))
;       )

(def product-col-ref (firestore/collection-ref ["product"]))

(def user-col-ref (firestore/collection-ref ["user"]))

(defn get-sale-col-ref-for-user
  "Returns a Firestore CollectionReference for the 'sale' subcollection
  under a specific user document. Returns nil if `user-uid` is nil."
  [user-uid]
  (when user-uid
    (firestore/collection-ref ["user" user-uid "sale"])))

(defn get-patient-col-ref
  "Returns a Firestore CollectionReference for the 'patient' subcollection
  under a specific user document. Returns nil if `user-uid` is nil."
  [user-uid]
  (when user-uid
    (firestore/collection-ref ["user" user-uid "patient"])))

(defn get-logged-in-user-atom
  "Returns a Reagent atom that tracks the currently logged-in user's data.
  This atom merges data from Firebase Auth (email, uid) and the corresponding
  user document in Firestore (e.g., admin/active status). It is reactive
  and updates when the auth state or the user document changes."
  []
  (r/with-let
    [[unsubscribe-fn auth-user-atom]
     (firebase/get-current-user-subscription-components)
     db-user-atom
     (r/reaction
      (when-let [auth-user @auth-user-atom]
        (let [doc-ref (firestore/document-ref user-col-ref (:uid auth-user))]
          ; (println "get-current-user db-user-atom reaction" "\rauth-user" auth-user "\rdoc-ref" doc-ref)
          @@(r/track firestore/get-doc-subscription-atom doc-ref))))
     merged-user-atom
     (r/reaction
      (when-let [auth-user @auth-user-atom]
        (if-let [db-user @db-user-atom]
          (merge db-user auth-user)
          auth-user)))
     ; uuid (random-uuid)
     ]
    merged-user-atom
    (finally
      (unsubscribe-fn))))

(defn get-logged-in-user-must-reset-password-atom
  "Returns a Reagent atom that tracks whether the currently logged-in user must reset their password.
  This atom is reactive and updates when the auth state or the user document changes."
  []
  (r/reaction
   (when-let [user @@(r/track get-logged-in-user-atom)]
     (:must-reset-password user))))

(defn get-implied-user-uid-atom
  "Returns a Reagent atom that tracks the 'implied' user UID.
  This is determined first by the user UID in the route parameters,
  and falls back to the UID of the currently logged-in user if no UID is
  present in the route. This is useful for views that can operate in the
  context of a specific user (from the URL) or the current user."
  []
  (r/reaction
   (if-let [uid-from-match @route-match-user-uid-atom]
     uid-from-match
     (let [uid-from-session (:uid @@(r/track get-logged-in-user-atom))]
       uid-from-session))))

(defn get-implied-user-sale-col-ref-atom
  "Returns a Reagent atom that tracks the Firestore CollectionReference
  for the 'sale' subcollection of the 'implied' user (determined by `get-implied-user-uid-atom`).
  This atom is reactive and updates when the implied user UID changes."
  []
  (r/with-let [user-uid-atom @(r/track get-implied-user-uid-atom)
               a (r/reaction @(r/track get-sale-col-ref-for-user @user-uid-atom))
               ; uuid (random-uuid)
               ]
    a))

(defn get-implied-user-patient-col-ref-atom
  "Returns a Reagent atom that tracks the Firestore CollectionReference
  for the 'patient' subcollection of the 'implied' user (determined by `get-implied-user-uid-atom`).
  This atom is reactive and updates when the implied user UID changes."
  []
  (r/with-let [user-uid-atom @(r/track get-implied-user-uid-atom)
               a (r/reaction @(r/track get-patient-col-ref @user-uid-atom))
               ; uuid (random-uuid)
               ]
    a))

; (r/track! #(println "get-sale-dbe-atom change:" @@(r/track get-sale-dbe-atom)))

(defn get-auth-ready-atom
  "Returns a Reagent atom that becomes true when Firebase Authentication is initialized and ready.
  This is useful for delaying rendering or actions until the authentication state is known."
  []
  (r/with-let [a (r/atom false)
               auth-ready-c (firebase/<?auth-ready)
               _ (go (<! auth-ready-c) (reset! a true))]
    a))

(defn get-all-patients-atom
  "Returns a Reagent atom that tracks a real-time subscription to all 'patient' documents
  across all user subcollections using a collection group query.
  This atom is reactive and updates as patient data changes in Firestore."
  []
  @(r/track
    firestore/get-collection-subscription-atom
    (firestore/collection-group-ref "patient")))

(defn get-implied-user-patients-atom
  "Returns a Reagent atom that tracks a real-time subscription to the 'patient' documents
  for the 'implied' user (determined by `get-implied-user-uid-atom`).
  This atom is reactive and updates as the implied user's patient data changes in Firestore."
  []
  (r/reaction
   (when-let [patient-col-ref @@(r/track get-implied-user-patient-col-ref-atom)]
     @@(r/track firestore/get-collection-subscription-atom patient-col-ref))))

(defn get-user-patients-atom
  "Returns a Reagent atom that tracks a real-time subscription to the 'patient' documents
  for a specific user identified by `user-id`.
  This atom is reactive and updates as the specified user's patient data changes in Firestore."
  [user-id]
  (r/reaction
   (when-let [user-patient-col-ref (get-patient-col-ref user-id)]
     @@(r/track firestore/get-collection-subscription-atom user-patient-col-ref))))

(defn get-all-sales-atom
  "Returns a Reagent atom that tracks a real-time subscription to all 'sale' documents
  across all user subcollections using a collection group query.
  This atom is reactive and updates as sale data changes in Firestore."
  []
  @(r/track
    firestore/get-collection-subscription-atom
    (firestore/collection-group-ref "sale")))

(defn get-all-active-sales-atom
  "Returns a Reagent atom that tracks a real-time subscription to all 'sale' documents
  across all user subcollections where the 'active' field is true.
  This atom is reactive and updates as active sale data changes in Firestore."
  []
  @(r/track
    firestore/get-collection-subscription-atom
    (firestore/collection-group-ref "sale" [:where "active" "==" true])))

(defn get-all-valid-sales-expiration-ordered-atom
  "Returns a Reagent atom that tracks a real-time subscription to all 'sale' documents
  across all user subcollections where the 'invalid' field is false, ordered by
  'stock-expiration-date' in ascending order.
  This atom is reactive and updates as valid sale data changes in Firestore."
  []
  @(r/track
    firestore/get-collection-subscription-atom
    (firestore/collection-group-ref
     "sale"
     [[:order-by "stock-expiration-date" "asc"]
      [:where "invalid" "==" false]])))

(defn get-user-valid-sold-sales-expiration-ordered-atom
  "Returns a Reagent atom that tracks a real-time subscription to 'sale' documents
  across all user subcollections where the 'invalid' field is false and the
  'sold-by' field matches the given `user-id`, ordered by 'stock-expiration-date'
  in ascending order.
  This atom is reactive and updates as the specified user's valid sold sale data changes in Firestore."
  [user-id]
  @(r/track
    firestore/get-collection-subscription-atom
    (firestore/collection-group-ref
     "sale"
     [[:where "invalid" "==" false]
      [:where "sold-by" "==" user-id]])))

#_(defn get-user-valid-sales-expiration-ordered-atom [user-id]
    (r/reaction
     (let [user-sale-col-ref (get-sale-col-ref-for-user user-id)
           user-sale-expiration-ordered-col-ref
           (firestore/reset-constraints
            user-sale-col-ref
            [[:order-by "stock-expiration-date" "asc"]
             [:where "invalid" "==" false]])]
       @@(r/track
          firestore/get-collection-subscription-atom
          user-sale-expiration-ordered-col-ref))))

(defn get-implied-user-sales-atom
  "Returns a Reagent atom that tracks a real-time subscription to the 'sale' documents
  for the 'implied' user (determined by `get-implied-user-uid-atom`).
  This atom is reactive and updates as the implied user's sale data changes in Firestore."
  []
  (r/reaction
   (when-let [sale-col-ref @@(r/track get-implied-user-sale-col-ref-atom)]
     @@(r/track firestore/get-collection-subscription-atom sale-col-ref))))

(defn get-user-sales-atom
  "Returns a Reagent atom that tracks a real-time subscription to the 'sale' documents
  for a specific user identified by `user-id`.
  This atom is reactive and updates as the specified user's sale data changes in Firestore."
  [user-id]
  (r/reaction
   (let [user-sale-col-ref (get-sale-col-ref-for-user user-id)]
     @@(r/track firestore/get-collection-subscription-atom user-sale-col-ref))))

(defn get-user-active-sales-atom
  "Returns a Reagent atom that tracks a real-time subscription to the 'sale' documents
  for a specific user identified by `user-id` where the 'active' field is true.
  This atom is reactive and updates as the specified user's active sale data changes in Firestore."
  [user-id]
  (r/reaction
   (let [user-sale-col-ref (get-sale-col-ref-for-user user-id)
         user-active-sale-col-ref
         (firestore/add-constraints
          user-sale-col-ref
          [:where "active" "==" true])]
     @@(r/track
        firestore/get-collection-subscription-atom
        user-active-sale-col-ref))))

(defn get-products-atom
  "Returns a Reagent atom that tracks a real-time subscription to all 'product' documents.
  This atom is reactive and updates as product data changes in Firestore."
  []
  (firestore/get-collection-subscription-atom product-col-ref))

(defn get-id-to-product-map-atom
  "Returns a Reagent atom that tracks a map of product documents, keyed by their document ID.
  This atom is derived from the `get-products-atom` and updates reactively
  when the underlying product data changes."
  []
  (r/reaction
   (let [all-products-atom @(r/track get-products-atom)]
     (lang/index-using @all-products-atom firestore/get-doc-id))))

(defn get-id-to-patient-map-atom
  "Returns a Reagent atom that tracks a map of patient documents, keyed by their document ID.
  If the logged-in user is an admin, it tracks all patients (`get-all-patients-atom`).
  Otherwise, it tracks only the patients for the 'implied' user (`get-implied-user-patients-atom`).
  This atom is reactive and updates when the underlying patient data or user's admin status changes."
  []
  (r/reaction
   (when-let [user @@(r/track get-logged-in-user-atom)]
     (let [admin (-> user :admin)
           patients-atom
           (if admin
             @(r/track get-all-patients-atom)
             (when-let [patient-col-ref @@(r/track get-implied-user-patient-col-ref-atom)]
               @(r/track firestore/get-collection-subscription-atom patient-col-ref)))]
       (lang/index-using @patients-atom firestore/get-doc-id)))))

(defn get-id-to-user-map-atom
  "Returns a Reagent atom that tracks a map of user documents, keyed by their document ID.
  This atom is derived from the subscription to the 'user' collection and updates reactively
  when the underlying user data changes."
  []
  (r/reaction
   (let [users-atom @(r/track firestore/get-collection-subscription-atom user-col-ref)]
     (lang/index-using @users-atom firestore/get-doc-id))))

(defn get-implied-user-sales-to-patient-atom
  "Returns a Reagent atom that tracks a real-time subscription to the 'sale' documents
  for the 'implied' user (determined by `get-implied-user-uid-atom`) that are associated
  with a specific `patient-id`.
  This atom is reactive and updates as the relevant sale data changes in Firestore."
  [patient-id]
  (r/reaction
   (when-let [sale-col-ref @@(r/track get-implied-user-sale-col-ref-atom)]
     @@(r/track
        firestore/get-collection-subscription-atom
        (firestore/add-constraints
         sale-col-ref
         [:where "patient" "==" patient-id])))))

(defn get-visible-patients-atom
  "Returns a Reagent atom that tracks the set of patient documents visible to the current user.
  If the user is an admin, it tracks all patients (`get-all-patients-atom`).
  Otherwise, it tracks only the patients for the 'implied' user (`get-implied-user-patients-atom`).
  This atom is reactive and updates based on the user's role and relevant patient data changes."
  []
  (r/reaction
   (when-let [user @@(r/track get-logged-in-user-atom)]
     (let [admin (-> user :admin)]
       (if admin
         @@(r/track get-all-patients-atom)
         @@(r/track get-implied-user-patients-atom))))))

(defn get-visible-sales-atom
  "Returns a Reagent atom that tracks the set of sale documents visible to the current user.
  If the user is an admin, it tracks all sales (`get-all-sales-atom`).
  Otherwise, it tracks only the sales for the 'implied' user (`get-implied-user-sales-atom`).
  This atom is reactive and updates based on the user's role and relevant sale data changes."
  []
  (r/reaction
   (when-let [user @@(r/track get-logged-in-user-atom)]
     (let [admin (-> user :admin)]
       (if admin
         @@(r/track get-all-sales-atom)
         @@(r/track get-implied-user-sales-atom))))))

(defn get-users-atom
  "Returns a Reagent atom that tracks a real-time subscription to all 'user' documents.
  This atom is reactive and updates as user data changes in Firestore."
  []
  @(r/track firestore/get-collection-subscription-atom user-col-ref))

(defn get-active-users-atom
  "Returns a Reagent atom that tracks a real-time subscription to 'user' documents
  where the 'active' field is true.
  This atom is reactive and updates as active user data changes in Firestore."
  []
  (let [active-user-col-ref
        (firestore/add-constraints
         user-col-ref
         [:where "active" "==" true])]
    @(r/track firestore/get-collection-subscription-atom active-user-col-ref)))

(defn get-user-atom
  "Returns a Reagent atom that tracks a real-time subscription to a specific user document
  identified by `user-id`.
  This atom is reactive and updates as the specified user's document changes in Firestore."
  [user-id]
  @(r/track
    firestore/get-doc-subscription-atom
    (firestore/document-ref user-col-ref user-id)))

(defn sale-to-serialized-doc-ref
  "Converts a sale document map into a serialized Firestore DocumentReference string.
  This is useful for storing references to sale documents, e.g., in other documents.
  Returns the serialized string if the sale document has an `assigned-to` user
  and a document ID, otherwise returns nil."
  [{:keys [assigned-to] :as sale_doc}]
  (let [doc-id (firestore/get-doc-id sale_doc)]
    (when (and assigned-to doc-id)
      (->
       (get-sale-col-ref-for-user assigned-to)
       (firestore/get-doc-ref doc-id)
       firestore/serialize-doc-ref))))

(def log-col-ref (firestore/collection-ref ["log"]))

(defn get-log-subscription-atom
  "Returns a Reagent atom that tracks a real-time subscription to the 'log' documents,
  ordered by timestamp in descending order.
  This atom is reactive and updates as new log entries are added to Firestore."
  []
  @(r/track
    firestore/get-collection-subscription-atom
    (->
     log-col-ref
     (firestore/add-constraints [:order-by "timestamp" "desc"]))))

(def preregistrations-col-ref (firestore/collection-ref ["preregistrations"]))